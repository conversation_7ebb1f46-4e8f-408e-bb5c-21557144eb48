// nmcu_minimal_tb.sv
// Minimal Verilator-compatible testbench for NMCU Chiplet
// Just instantiates DUT with tied-off signals to test compilation

`timescale 1ns/1ps

`include "parameters.sv"
`include "types.sv"

module nmcu_minimal_tb(
    input logic clk,
    input logic rst_n
);

    import nmcu_pkg::*;
    import nmcu_types::*;
    
    // Clock and reset are now external inputs
    
    // Interface signals (tied off for minimal test)
    // UCIe Interface signals
    logic                           ucie_tx_clk;
    logic                           ucie_rx_clk;
    logic [UCIE_FLIT_WIDTH-1:0]     ucie_tx_data;
    logic                           ucie_tx_valid;
    logic                           ucie_tx_ready;
    logic [UCIE_FLIT_WIDTH-1:0]     ucie_rx_data;
    logic                           ucie_rx_valid;
    logic                           ucie_rx_ready;
    
    // Memory Interface signals
    logic                           mem_req_valid;
    logic                           mem_req_ready;
    logic [ADDR_WIDTH-1:0]          mem_req_addr;
    logic [MEM_DATA_WIDTH-1:0]      mem_req_data;
    logic [2:0]                     mem_req_cmd;
    logic [7:0]                     mem_req_burst_len;
    logic [3:0]                     mem_req_id;
    logic [MEM_DATA_WIDTH/8-1:0]    mem_req_be;
    logic                           mem_resp_valid;
    logic                           mem_resp_ready;
    logic [MEM_DATA_WIDTH-1:0]      mem_resp_data;
    logic [3:0]                     mem_resp_id;
    logic                           mem_resp_error;
    logic [1:0]                     mem_resp_status;
    logic [7:0]                     mem_resp_tag;
    
    // DUT outputs
    logic [31:0]                    status_reg;
    logic                           ready;
    logic                           error;
    
    // Tie off inputs
    assign ucie_tx_ready = 1'b1;
    assign ucie_rx_data = '0;
    assign ucie_rx_valid = 1'b0;
    assign mem_req_ready = 1'b1;
    assign mem_resp_valid = 1'b0;
    assign mem_resp_data = '0;
    assign mem_resp_id = '0;
    assign mem_resp_error = 1'b0;
    assign mem_resp_status = 2'b00;
    assign mem_resp_tag = '0;

    // Clock assignments
    assign ucie_tx_clk = clk;
    assign ucie_rx_clk = clk;

    // DUT instantiation with individual signals (avoiding interfaces for now)
    nmcu_chiplet dut (
        .clk(clk),
        .rst_n(rst_n),

        // UCIe Interface - individual signals
        .ucie_tx_clk(ucie_tx_clk),
        .ucie_rx_clk(ucie_rx_clk),
        .ucie_tx_data(ucie_tx_data),
        .ucie_tx_valid(ucie_tx_valid),
        .ucie_tx_ready(ucie_tx_ready),
        .ucie_rx_data(ucie_rx_data),
        .ucie_rx_valid(ucie_rx_valid),
        .ucie_rx_ready(ucie_rx_ready),
        
        // Memory Interface - individual signals
        .mem_req_valid(mem_req_valid),
        .mem_req_ready(mem_req_ready),
        .mem_req_addr(mem_req_addr),
        .mem_req_data(mem_req_data),
        .mem_req_cmd(mem_req_cmd),
        .mem_req_burst_len(mem_req_burst_len),
        .mem_req_id(mem_req_id),
        .mem_req_be(mem_req_be),
        .mem_resp_valid(mem_resp_valid),
        .mem_resp_ready(mem_resp_ready),
        .mem_resp_data(mem_resp_data),
        .mem_resp_id(mem_resp_id),
        .mem_resp_error(mem_resp_error),
        .mem_resp_status(mem_resp_status),
        .mem_resp_tag(mem_resp_tag),
        
        // Status outputs
        .status_reg(status_reg),
        .ready(ready),
        .error(error)
    );
    
    // Test stimulus (Verilator compatible)
    logic [15:0] test_counter;

    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            test_counter <= 0;
        end else begin
            test_counter <= test_counter + 1;

            if (test_counter == 16'd10) begin
                $display("[TB] DUT instantiated successfully");
            end

            if (test_counter == 16'd1000) begin
                $display("[TB] Simulation completed");
                $finish;
            end
        end
    end

    initial begin
        $display("[TB] Starting minimal NMCU Chiplet simulation");
    end
    
    // Waveform dumping for Verilator
    initial begin
        if ($test$plusargs("trace")) begin
            $dumpfile("nmcu_minimal_tb.vcd");
            $dumpvars(0, nmcu_minimal_tb);
            $display("[TB] VCD tracing enabled");
        end
    end
    
    // Simulation timeout (Verilator compatible)
    logic [31:0] timeout_counter;

    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            timeout_counter <= 0;
        end else begin
            timeout_counter <= timeout_counter + 1;

            if (timeout_counter > 32'd100000) begin
                $display("[TB] ERROR: Simulation timeout!");
                $finish;
            end
        end
    end
    
    // Monitor key signals
    always @(posedge clk) begin
        if (rst_n && ready) begin
            $display("[TB] DUT is ready at time %0t", $time);
        end
        
        if (rst_n && error) begin
            $display("[TB] ERROR: DUT error signal asserted at time %0t", $time);
        end
    end

endmodule
