// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vnmcu_minimal_tb.h for the primary calling header

#include "Vnmcu_minimal_tb__pch.h"
#include "Vnmcu_minimal_tb___024root.h"

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___eval_static(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_static\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__Vtrigprevexpr___TOP__clk__0 = vlSelfRef.clk;
    vlSelfRef.__Vtrigprevexpr___TOP__rst_n__0 = vlSelfRef.rst_n;
}

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___eval_initial__TOP(Vnmcu_minimal_tb___024root* vlSelf);

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___eval_initial(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_initial\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    Vnmcu_minimal_tb___024root___eval_initial__TOP(vlSelf);
}

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___eval_final(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_final\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___eval_settle(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_settle\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

#ifdef VL_DEBUG
VL_ATTR_COLD void Vnmcu_minimal_tb___024root___dump_triggers__act(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___dump_triggers__act\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VactTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VactTriggered.word(0U))) {
        VL_DBG_MSGF("         'act' region trigger index 0 is active: @(posedge clk)\n");
    }
    if ((2ULL & vlSelfRef.__VactTriggered.word(0U))) {
        VL_DBG_MSGF("         'act' region trigger index 1 is active: @(negedge rst_n)\n");
    }
}
#endif  // VL_DEBUG

#ifdef VL_DEBUG
VL_ATTR_COLD void Vnmcu_minimal_tb___024root___dump_triggers__nba(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___dump_triggers__nba\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VnbaTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VL_DBG_MSGF("         'nba' region trigger index 0 is active: @(posedge clk)\n");
    }
    if ((2ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VL_DBG_MSGF("         'nba' region trigger index 1 is active: @(negedge rst_n)\n");
    }
}
#endif  // VL_DEBUG

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___ctor_var_reset(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___ctor_var_reset\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelf->clk = VL_RAND_RESET_I(1);
    vlSelf->rst_n = VL_RAND_RESET_I(1);
    vlSelf->nmcu_minimal_tb__DOT__test_counter = VL_RAND_RESET_I(16);
    vlSelf->nmcu_minimal_tb__DOT__timeout_counter = VL_RAND_RESET_I(32);
    vlSelf->nmcu_minimal_tb__DOT__dut__DOT__cycle_counter = VL_RAND_RESET_I(32);
    vlSelf->__Vtrigprevexpr___TOP__clk__0 = VL_RAND_RESET_I(1);
    vlSelf->__Vtrigprevexpr___TOP__rst_n__0 = VL_RAND_RESET_I(1);
    for (int __Vi0 = 0; __Vi0 < 2; ++__Vi0) {
        vlSelf->__Vm_traceActivity[__Vi0] = 0;
    }
}
