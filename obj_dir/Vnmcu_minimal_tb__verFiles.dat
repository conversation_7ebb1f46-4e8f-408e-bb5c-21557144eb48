# DESCRIPTION: Verilator output: Timestamp data for --skip-identical.  Delete at will.
C "--sv --cc --exe --build --trace --trace-fst --no-timing --timescale 1ns/1ps -top nmcu_minimal_tb -y src -y src/cache -y src/common -y src/control -y src/interconnect -y src/memory_if -y src/pe_array -y src/top -y sim/models -y sim/tb/interfaces -y sim/tb/simple -Mdir ./obj_dir -o nmcu_minimal_tb_sim src/common/parameters.sv src/common/types.sv src/top/nmcu_chiplet_wrapper.sv sim/tb/simple/nmcu_minimal_tb.sv sim/tb/simple/nmcu_minimal_tb.cpp"
T      5890 27792400  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb.cpp"
T      3689 27792399  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb.h"
T      2112 27792412  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb.mk"
T       589 27792398  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__ConstPool_0.cpp"
T      1680 27792396  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__Syms.cpp"
T      1525 27792397  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__Syms.h"
T       326 27792409  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__TraceDecls__0__Slow.cpp"
T      2366 27792410  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__Trace__0.cpp"
T     23481 27792408  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__Trace__0__Slow.cpp"
T      1289 27792402  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb___024root.h"
T      8255 27792407  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb___024root__DepSet_h0fd5b54b__0.cpp"
T      4265 27792405  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb___024root__DepSet_h0fd5b54b__0__Slow.cpp"
T      1343 27792406  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb___024root__DepSet_hacd515fc__0.cpp"
T      1139 27792404  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb___024root__DepSet_hacd515fc__0__Slow.cpp"
T       776 27792403  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb___024root__Slow.cpp"
T       806 27792401  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__pch.h"
T      1186 27792413  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__ver.d"
T         0        0  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb__verFiles.dat"
T      1978 27792411  1749909043           0  1749909043           0 "./obj_dir/Vnmcu_minimal_tb_classes.mk"
S  10077424 26339493  1749262563           0  1745773137           0 "/opt/homebrew/Cellar/verilator/5.036/bin/verilator_bin"
S      6525 26339612  1749262563           0  1745773137           0 "/opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_std.sv"
S      2787 26339613  1749262563           0  1745773137           0 "/opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_std_waiver.vlt"
S      5215 27791211  1749908715           0  1749908715           0 "sim/tb/simple/nmcu_minimal_tb.sv"
S      5827 27750481  1749903485           0  1749903485           0 "src/common/parameters.sv"
S      8822 27750482  1749907825           0  1749907825           0 "src/common/types.sv"
S      3269 27791664  1749908289           0  1749908289           0 "src/top/nmcu_chiplet_wrapper.sv"
