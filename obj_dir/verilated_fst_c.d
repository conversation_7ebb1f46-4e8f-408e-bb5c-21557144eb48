verilated_fst_c.o: \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_fst_c.cpp \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilatedos.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_config.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_types.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_funcs.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_fst_c.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_trace.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/gtkwave/fastlz.c \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/gtkwave/fastlz.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/gtkwave/fstapi.c \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/gtkwave/fstapi.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/gtkwave/lz4.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/gtkwave/lz4.c \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_trace_imp.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_intrinsics.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_threads.h
