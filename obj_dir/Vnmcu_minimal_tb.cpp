// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Model implementation (design independent parts)

#include "Vnmcu_minimal_tb__pch.h"
#include "verilated_fst_c.h"

//============================================================
// Constructors

Vnmcu_minimal_tb::Vnmcu_minimal_tb(VerilatedContext* _vcontextp__, const char* _vcname__)
    : VerilatedModel{*_vcontextp__}
    , vlSymsp{new Vnmcu_minimal_tb__Syms(contextp(), _vcname__, this)}
    , clk{vlSymsp->TOP.clk}
    , rst_n{vlSymsp->TOP.rst_n}
    , rootp{&(vlSymsp->TOP)}
{
    // Register model with the context
    contextp()->addModel(this);
    contextp()->traceBaseModelCbAdd(
        [this](VerilatedTraceBaseC* tfp, int levels, int options) { traceBaseModel(tfp, levels, options); });
}

Vnmcu_minimal_tb::Vnmcu_minimal_tb(const char* _vcname__)
    : Vnmcu_minimal_tb(Verilated::threadContextp(), _vcname__)
{
}

//============================================================
// Destructor

Vnmcu_minimal_tb::~Vnmcu_minimal_tb() {
    delete vlSymsp;
}

//============================================================
// Evaluation function

#ifdef VL_DEBUG
void Vnmcu_minimal_tb___024root___eval_debug_assertions(Vnmcu_minimal_tb___024root* vlSelf);
#endif  // VL_DEBUG
void Vnmcu_minimal_tb___024root___eval_static(Vnmcu_minimal_tb___024root* vlSelf);
void Vnmcu_minimal_tb___024root___eval_initial(Vnmcu_minimal_tb___024root* vlSelf);
void Vnmcu_minimal_tb___024root___eval_settle(Vnmcu_minimal_tb___024root* vlSelf);
void Vnmcu_minimal_tb___024root___eval(Vnmcu_minimal_tb___024root* vlSelf);

void Vnmcu_minimal_tb::eval_step() {
    VL_DEBUG_IF(VL_DBG_MSGF("+++++TOP Evaluate Vnmcu_minimal_tb::eval_step\n"); );
#ifdef VL_DEBUG
    // Debug assertions
    Vnmcu_minimal_tb___024root___eval_debug_assertions(&(vlSymsp->TOP));
#endif  // VL_DEBUG
    vlSymsp->__Vm_activity = true;
    vlSymsp->__Vm_deleter.deleteAll();
    if (VL_UNLIKELY(!vlSymsp->__Vm_didInit)) {
        vlSymsp->__Vm_didInit = true;
        VL_DEBUG_IF(VL_DBG_MSGF("+ Initial\n"););
        Vnmcu_minimal_tb___024root___eval_static(&(vlSymsp->TOP));
        Vnmcu_minimal_tb___024root___eval_initial(&(vlSymsp->TOP));
        Vnmcu_minimal_tb___024root___eval_settle(&(vlSymsp->TOP));
    }
    VL_DEBUG_IF(VL_DBG_MSGF("+ Eval\n"););
    Vnmcu_minimal_tb___024root___eval(&(vlSymsp->TOP));
    // Evaluate cleanup
    Verilated::endOfEval(vlSymsp->__Vm_evalMsgQp);
}

void Vnmcu_minimal_tb::eval_end_step() {
    VL_DEBUG_IF(VL_DBG_MSGF("+eval_end_step Vnmcu_minimal_tb::eval_end_step\n"); );
#ifdef VM_TRACE
    // Tracing
    if (VL_UNLIKELY(vlSymsp->__Vm_dumping)) vlSymsp->_traceDump();
#endif  // VM_TRACE
}

//============================================================
// Events and timing
bool Vnmcu_minimal_tb::eventsPending() { return false; }

uint64_t Vnmcu_minimal_tb::nextTimeSlot() {
    VL_FATAL_MT(__FILE__, __LINE__, "", "No delays in the design");
    return 0;
}

//============================================================
// Utilities

const char* Vnmcu_minimal_tb::name() const {
    return vlSymsp->name();
}

//============================================================
// Invoke final blocks

void Vnmcu_minimal_tb___024root___eval_final(Vnmcu_minimal_tb___024root* vlSelf);

VL_ATTR_COLD void Vnmcu_minimal_tb::final() {
    Vnmcu_minimal_tb___024root___eval_final(&(vlSymsp->TOP));
}

//============================================================
// Implementations of abstract methods from VerilatedModel

const char* Vnmcu_minimal_tb::hierName() const { return vlSymsp->name(); }
const char* Vnmcu_minimal_tb::modelName() const { return "Vnmcu_minimal_tb"; }
unsigned Vnmcu_minimal_tb::threads() const { return 1; }
void Vnmcu_minimal_tb::prepareClone() const { contextp()->prepareClone(); }
void Vnmcu_minimal_tb::atClone() const {
    contextp()->threadPoolpOnClone();
}
std::unique_ptr<VerilatedTraceConfig> Vnmcu_minimal_tb::traceConfig() const {
    return std::unique_ptr<VerilatedTraceConfig>{new VerilatedTraceConfig{false, false, false}};
};

//============================================================
// Trace configuration

void Vnmcu_minimal_tb___024root__trace_decl_types(VerilatedFst* tracep);

void Vnmcu_minimal_tb___024root__trace_init_top(Vnmcu_minimal_tb___024root* vlSelf, VerilatedFst* tracep);

VL_ATTR_COLD static void trace_init(void* voidSelf, VerilatedFst* tracep, uint32_t code) {
    // Callback from tracep->open()
    Vnmcu_minimal_tb___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vnmcu_minimal_tb___024root*>(voidSelf);
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (!vlSymsp->_vm_contextp__->calcUnusedSigs()) {
        VL_FATAL_MT(__FILE__, __LINE__, __FILE__,
            "Turning on wave traces requires Verilated::traceEverOn(true) call before time 0.");
    }
    vlSymsp->__Vm_baseCode = code;
    tracep->pushPrefix(std::string{vlSymsp->name()}, VerilatedTracePrefixType::SCOPE_MODULE);
    Vnmcu_minimal_tb___024root__trace_decl_types(tracep);
    Vnmcu_minimal_tb___024root__trace_init_top(vlSelf, tracep);
    tracep->popPrefix();
}

VL_ATTR_COLD void Vnmcu_minimal_tb___024root__trace_register(Vnmcu_minimal_tb___024root* vlSelf, VerilatedFst* tracep);

VL_ATTR_COLD void Vnmcu_minimal_tb::traceBaseModel(VerilatedTraceBaseC* tfp, int levels, int options) {
    (void)levels; (void)options;
    VerilatedFstC* const stfp = dynamic_cast<VerilatedFstC*>(tfp);
    if (VL_UNLIKELY(!stfp)) {
        vl_fatal(__FILE__, __LINE__, __FILE__,"'Vnmcu_minimal_tb::trace()' called on non-VerilatedFstC object;"
            " use --trace-fst with VerilatedFst object, and --trace-vcd with VerilatedVcd object");
    }
    stfp->spTrace()->addModel(this);
    stfp->spTrace()->addInitCb(&trace_init, &(vlSymsp->TOP));
    Vnmcu_minimal_tb___024root__trace_register(&(vlSymsp->TOP), stfp->spTrace());
}
