verilated.o: \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated.cpp \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_config.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilatedos.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_imp.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_types.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_funcs.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_syms.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_sym_props.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_threads.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_trace.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilatedos_c.h
