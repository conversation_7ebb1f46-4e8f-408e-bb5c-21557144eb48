// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Symbol table internal header
//
// Internal details; most calling programs do not need this header,
// unless using verilator public meta comments.

#ifndef VERILATED_VNMCU_MINIMAL_TB__SYMS_H_
#define VERILATED_VNMCU_MINIMAL_TB__SYMS_H_  // guard

#include "verilated.h"
#include "verilated_fst_c.h"

// INCLUDE MODEL CLASS

#include "Vnmcu_minimal_tb.h"

// INCLUDE MODULE CLASSES
#include "Vnmcu_minimal_tb___024root.h"

// SYMS CLASS (contains all model state)
class alignas(VL_CACHE_LINE_BYTES)Vnmcu_minimal_tb__Syms final : public VerilatedSyms {
  public:
    // INTERNAL STATE
    Vnmcu_minimal_tb* const __Vm_modelp;
    bool __Vm_dumping = false;  // Dumping is active
    VerilatedMutex __Vm_dumperMutex;  // Protect __Vm_dumperp
    VerilatedFstC* __Vm_dumperp VL_GUARDED_BY(__Vm_dumperMutex) = nullptr;  /// Trace class for $dump*
    bool __Vm_activity = false;  ///< Used by trace routines to determine change occurred
    uint32_t __Vm_baseCode = 0;  ///< Used by trace routines when tracing multiple models
    VlDeleter __Vm_deleter;
    bool __Vm_didInit = false;

    // MODULE INSTANCE STATE
    Vnmcu_minimal_tb___024root     TOP;

    // CONSTRUCTORS
    Vnmcu_minimal_tb__Syms(VerilatedContext* contextp, const char* namep, Vnmcu_minimal_tb* modelp);
    ~Vnmcu_minimal_tb__Syms();

    // METHODS
    const char* name() { return TOP.name(); }
    void _traceDump();
    void _traceDumpOpen();
    void _traceDumpClose();
};

#endif  // guard
