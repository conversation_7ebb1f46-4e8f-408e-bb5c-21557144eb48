// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Constant pool
//

#include "verilated.h"

extern const VlWide<16>/*511:0*/ Vnmcu_minimal_tb__ConstPool__CONST_h93e1b771_0 = {{
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000
}};

extern const VlWide<8>/*255:0*/ Vnmcu_minimal_tb__ConstPool__CONST_h9e67c271_0 = {{
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000
}};
