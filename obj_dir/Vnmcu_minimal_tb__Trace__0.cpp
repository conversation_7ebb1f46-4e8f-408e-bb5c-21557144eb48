// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Tracing implementation internals
#include "verilated_fst_c.h"
#include "Vnmcu_minimal_tb__Syms.h"


void Vnmcu_minimal_tb___024root__trace_chg_0_sub_0(Vnmcu_minimal_tb___024root* vlSelf, VerilatedFst::Buffer* bufp);

void Vnmcu_minimal_tb___024root__trace_chg_0(void* voidSelf, VerilatedFst::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root__trace_chg_0\n"); );
    // Init
    Vnmcu_minimal_tb___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vnmcu_minimal_tb___024root*>(voidSelf);
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (VL_UNLIKELY(!vlSymsp->__Vm_activity)) return;
    // Body
    Vnmcu_minimal_tb___024root__trace_chg_0_sub_0((&vlSymsp->TOP), bufp);
}

void Vnmcu_minimal_tb___024root__trace_chg_0_sub_0(Vnmcu_minimal_tb___024root* vlSelf, VerilatedFst::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root__trace_chg_0_sub_0\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode + 1);
    // Body
    if (VL_UNLIKELY((vlSelfRef.__Vm_traceActivity[1U]))) {
        bufp->chgIData(oldp+0,(vlSelfRef.nmcu_minimal_tb__DOT__dut__DOT__cycle_counter),32);
        bufp->chgSData(oldp+1,(vlSelfRef.nmcu_minimal_tb__DOT__test_counter),16);
        bufp->chgIData(oldp+2,(vlSelfRef.nmcu_minimal_tb__DOT__timeout_counter),32);
    }
    bufp->chgBit(oldp+3,(vlSelfRef.clk));
    bufp->chgBit(oldp+4,(vlSelfRef.rst_n));
    bufp->chgBit(oldp+5,(((IData)(vlSelfRef.rst_n) 
                          & (0x64U < vlSelfRef.nmcu_minimal_tb__DOT__dut__DOT__cycle_counter))));
}

void Vnmcu_minimal_tb___024root__trace_cleanup(void* voidSelf, VerilatedFst* /*unused*/) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root__trace_cleanup\n"); );
    // Init
    Vnmcu_minimal_tb___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vnmcu_minimal_tb___024root*>(voidSelf);
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    // Body
    vlSymsp->__Vm_activity = false;
    vlSymsp->TOP.__Vm_traceActivity[0U] = 0U;
    vlSymsp->TOP.__Vm_traceActivity[1U] = 0U;
}
