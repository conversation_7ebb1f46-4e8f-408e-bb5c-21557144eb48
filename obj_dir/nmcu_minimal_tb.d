nmcu_minimal_tb.o: ../sim/tb/simple/nmcu_minimal_tb.cpp \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilatedos.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_config.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_types.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_funcs.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_fst_c.h \
  /opt/homebrew/Cellar/verilator/5.036/share/verilator/include/verilated_trace.h \
  Vnmcu_minimal_tb.h
