// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vnmcu_minimal_tb.h for the primary calling header

#include "Vnmcu_minimal_tb__pch.h"
#include "Vnmcu_minimal_tb__Syms.h"
#include "Vnmcu_minimal_tb___024root.h"

void Vnmcu_minimal_tb___024root___ctor_var_reset(Vnmcu_minimal_tb___024root* vlSelf);

Vnmcu_minimal_tb___024root::Vnmcu_minimal_tb___024root(Vnmcu_minimal_tb__Syms* symsp, const char* v__name)
    : VerilatedModule{v__name}
    , vlSymsp{symsp}
 {
    // Reset structure values
    Vnmcu_minimal_tb___024root___ctor_var_reset(this);
}

void Vnmcu_minimal_tb___024root::__Vconfigure(bool first) {
    (void)first;  // Prevent unused variable warning
}

Vnmcu_minimal_tb___024root::~Vnmcu_minimal_tb___024root() {
}
