// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design internal header
// See Vnmcu_minimal_tb.h for the primary calling header

#ifndef VERILATED_VNMCU_MINIMAL_TB___024ROOT_H_
#define VERILATED_VNMCU_MINIMAL_TB___024ROOT_H_  // guard

#include "verilated.h"


class Vnmcu_minimal_tb__Syms;

class alignas(VL_CACHE_LINE_BYTES) Vnmcu_minimal_tb___024root final : public VerilatedModule {
  public:

    // DESIGN SPECIFIC STATE
    VL_IN8(clk,0,0);
    VL_IN8(rst_n,0,0);
    CData/*0:0*/ __Vtrigprevexpr___TOP__clk__0;
    CData/*0:0*/ __Vtrigprevexpr___TOP__rst_n__0;
    CData/*0:0*/ __VactContinue;
    SData/*15:0*/ nmcu_minimal_tb__DOT__test_counter;
    IData/*31:0*/ nmcu_minimal_tb__DOT__timeout_counter;
    IData/*31:0*/ nmcu_minimal_tb__DOT__dut__DOT__cycle_counter;
    IData/*31:0*/ __VactIterCount;
    VlUnpacked<CData/*0:0*/, 2> __Vm_traceActivity;
    VlTriggerVec<2> __VactTriggered;
    VlTriggerVec<2> __VnbaTriggered;

    // INTERNAL VARIABLES
    Vnmcu_minimal_tb__Syms* const vlSymsp;

    // CONSTRUCTORS
    Vnmcu_minimal_tb___024root(Vnmcu_minimal_tb__Syms* symsp, const char* v__name);
    ~Vnmcu_minimal_tb___024root();
    VL_UNCOPYABLE(Vnmcu_minimal_tb___024root);

    // INTERNAL METHODS
    void __Vconfigure(bool first);
};


#endif  // guard
