// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vnmcu_minimal_tb.h for the primary calling header

#include "Vnmcu_minimal_tb__pch.h"
#include "Vnmcu_minimal_tb___024root.h"

void Vnmcu_minimal_tb___024root___eval_act(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_act\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

void Vnmcu_minimal_tb___024root___nba_sequent__TOP__0(Vnmcu_minimal_tb___024root* vlSelf);
void Vnmcu_minimal_tb___024root___nba_sequent__TOP__1(Vnmcu_minimal_tb___024root* vlSelf);

void Vnmcu_minimal_tb___024root___eval_nba(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_nba\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        Vnmcu_minimal_tb___024root___nba_sequent__TOP__0(vlSelf);
    }
    if ((3ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        Vnmcu_minimal_tb___024root___nba_sequent__TOP__1(vlSelf);
        vlSelfRef.__Vm_traceActivity[1U] = 1U;
    }
}

VL_INLINE_OPT void Vnmcu_minimal_tb___024root___nba_sequent__TOP__0(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___nba_sequent__TOP__0\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if (VL_UNLIKELY((((IData)(vlSelfRef.rst_n) & (0x64U 
                                                  < vlSelfRef.nmcu_minimal_tb__DOT__dut__DOT__cycle_counter))))) {
        VL_WRITEF_NX("[TB] DUT is ready at time %0t\n",0,
                     64,VL_TIME_UNITED_Q(1000),-9);
    }
}

VL_INLINE_OPT void Vnmcu_minimal_tb___024root___nba_sequent__TOP__1(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___nba_sequent__TOP__1\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    SData/*15:0*/ __Vdly__nmcu_minimal_tb__DOT__test_counter;
    __Vdly__nmcu_minimal_tb__DOT__test_counter = 0;
    IData/*31:0*/ __Vdly__nmcu_minimal_tb__DOT__timeout_counter;
    __Vdly__nmcu_minimal_tb__DOT__timeout_counter = 0;
    // Body
    __Vdly__nmcu_minimal_tb__DOT__test_counter = vlSelfRef.nmcu_minimal_tb__DOT__test_counter;
    __Vdly__nmcu_minimal_tb__DOT__timeout_counter = vlSelfRef.nmcu_minimal_tb__DOT__timeout_counter;
    if (vlSelfRef.rst_n) {
        __Vdly__nmcu_minimal_tb__DOT__test_counter 
            = (0xffffU & ((IData)(1U) + (IData)(vlSelfRef.nmcu_minimal_tb__DOT__test_counter)));
        if (VL_UNLIKELY(((0xaU == (IData)(vlSelfRef.nmcu_minimal_tb__DOT__test_counter))))) {
            VL_WRITEF_NX("[TB] DUT instantiated successfully\n",0);
        }
        if (VL_UNLIKELY(((0x3e8U == (IData)(vlSelfRef.nmcu_minimal_tb__DOT__test_counter))))) {
            VL_WRITEF_NX("[TB] Simulation completed\n",0);
            VL_FINISH_MT("sim/tb/simple/nmcu_minimal_tb.sv", 122, "");
        }
    } else {
        __Vdly__nmcu_minimal_tb__DOT__test_counter = 0U;
    }
    if (vlSelfRef.rst_n) {
        __Vdly__nmcu_minimal_tb__DOT__timeout_counter 
            = ((IData)(1U) + vlSelfRef.nmcu_minimal_tb__DOT__timeout_counter);
        if (VL_UNLIKELY(((0x186a0U < vlSelfRef.nmcu_minimal_tb__DOT__timeout_counter)))) {
            VL_WRITEF_NX("[TB] ERROR: Simulation timeout!\n",0);
            VL_FINISH_MT("sim/tb/simple/nmcu_minimal_tb.sv", 151, "");
        }
    } else {
        __Vdly__nmcu_minimal_tb__DOT__timeout_counter = 0U;
    }
    vlSelfRef.nmcu_minimal_tb__DOT__dut__DOT__cycle_counter 
        = ((IData)(vlSelfRef.rst_n) ? ((IData)(1U) 
                                       + vlSelfRef.nmcu_minimal_tb__DOT__dut__DOT__cycle_counter)
            : 0U);
    vlSelfRef.nmcu_minimal_tb__DOT__test_counter = __Vdly__nmcu_minimal_tb__DOT__test_counter;
    vlSelfRef.nmcu_minimal_tb__DOT__timeout_counter 
        = __Vdly__nmcu_minimal_tb__DOT__timeout_counter;
}

void Vnmcu_minimal_tb___024root___eval_triggers__act(Vnmcu_minimal_tb___024root* vlSelf);

bool Vnmcu_minimal_tb___024root___eval_phase__act(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_phase__act\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    VlTriggerVec<2> __VpreTriggered;
    CData/*0:0*/ __VactExecute;
    // Body
    Vnmcu_minimal_tb___024root___eval_triggers__act(vlSelf);
    __VactExecute = vlSelfRef.__VactTriggered.any();
    if (__VactExecute) {
        __VpreTriggered.andNot(vlSelfRef.__VactTriggered, vlSelfRef.__VnbaTriggered);
        vlSelfRef.__VnbaTriggered.thisOr(vlSelfRef.__VactTriggered);
        Vnmcu_minimal_tb___024root___eval_act(vlSelf);
    }
    return (__VactExecute);
}

bool Vnmcu_minimal_tb___024root___eval_phase__nba(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_phase__nba\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*0:0*/ __VnbaExecute;
    // Body
    __VnbaExecute = vlSelfRef.__VnbaTriggered.any();
    if (__VnbaExecute) {
        Vnmcu_minimal_tb___024root___eval_nba(vlSelf);
        vlSelfRef.__VnbaTriggered.clear();
    }
    return (__VnbaExecute);
}

#ifdef VL_DEBUG
VL_ATTR_COLD void Vnmcu_minimal_tb___024root___dump_triggers__nba(Vnmcu_minimal_tb___024root* vlSelf);
#endif  // VL_DEBUG
#ifdef VL_DEBUG
VL_ATTR_COLD void Vnmcu_minimal_tb___024root___dump_triggers__act(Vnmcu_minimal_tb___024root* vlSelf);
#endif  // VL_DEBUG

void Vnmcu_minimal_tb___024root___eval(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    IData/*31:0*/ __VnbaIterCount;
    CData/*0:0*/ __VnbaContinue;
    // Body
    __VnbaIterCount = 0U;
    __VnbaContinue = 1U;
    while (__VnbaContinue) {
        if (VL_UNLIKELY(((0x64U < __VnbaIterCount)))) {
#ifdef VL_DEBUG
            Vnmcu_minimal_tb___024root___dump_triggers__nba(vlSelf);
#endif
            VL_FATAL_MT("sim/tb/simple/nmcu_minimal_tb.sv", 10, "", "NBA region did not converge.");
        }
        __VnbaIterCount = ((IData)(1U) + __VnbaIterCount);
        __VnbaContinue = 0U;
        vlSelfRef.__VactIterCount = 0U;
        vlSelfRef.__VactContinue = 1U;
        while (vlSelfRef.__VactContinue) {
            if (VL_UNLIKELY(((0x64U < vlSelfRef.__VactIterCount)))) {
#ifdef VL_DEBUG
                Vnmcu_minimal_tb___024root___dump_triggers__act(vlSelf);
#endif
                VL_FATAL_MT("sim/tb/simple/nmcu_minimal_tb.sv", 10, "", "Active region did not converge.");
            }
            vlSelfRef.__VactIterCount = ((IData)(1U) 
                                         + vlSelfRef.__VactIterCount);
            vlSelfRef.__VactContinue = 0U;
            if (Vnmcu_minimal_tb___024root___eval_phase__act(vlSelf)) {
                vlSelfRef.__VactContinue = 1U;
            }
        }
        if (Vnmcu_minimal_tb___024root___eval_phase__nba(vlSelf)) {
            __VnbaContinue = 1U;
        }
    }
}

#ifdef VL_DEBUG
void Vnmcu_minimal_tb___024root___eval_debug_assertions(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_debug_assertions\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if (VL_UNLIKELY(((vlSelfRef.clk & 0xfeU)))) {
        Verilated::overWidthError("clk");}
    if (VL_UNLIKELY(((vlSelfRef.rst_n & 0xfeU)))) {
        Verilated::overWidthError("rst_n");}
}
#endif  // VL_DEBUG
