// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vnmcu_minimal_tb.h for the primary calling header

#include "Vnmcu_minimal_tb__pch.h"
#include "Vnmcu_minimal_tb__Syms.h"
#include "Vnmcu_minimal_tb___024root.h"

VL_ATTR_COLD void Vnmcu_minimal_tb___024root___eval_initial__TOP(Vnmcu_minimal_tb___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vnmcu_minimal_tb___024root___eval_initial__TOP\n"); );
    Vnmcu_minimal_tb__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    VlWide<5>/*159:0*/ __Vtemp_1;
    // Body
    VL_WRITEF_NX("[TB] Starting minimal NMCU Chiplet simulation\n",0);
    if (VL_UNLIKELY((VL_TESTPLUSARGS_I(std::string{"trace"})))) {
        __Vtemp_1[0U] = 0x2e766364U;
        __Vtemp_1[1U] = 0x6c5f7462U;
        __Vtemp_1[2U] = 0x6e696d61U;
        __Vtemp_1[3U] = 0x755f6d69U;
        __Vtemp_1[4U] = 0x6e6d63U;
        vlSymsp->_vm_contextp__->dumpfile(VL_CVT_PACK_STR_NW(5, __Vtemp_1));
        vlSymsp->_traceDumpOpen();
        VL_WRITEF_NX("[TB] VCD tracing enabled\n",0);
    }
}
