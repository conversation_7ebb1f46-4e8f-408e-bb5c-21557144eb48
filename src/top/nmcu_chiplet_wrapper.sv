// nmcu_chiplet_wrapper.sv
// Simple wrapper for NMCU Chiplet that uses individual signals instead of interfaces
// This is for Verilator compatibility testing

`include "parameters.sv"
`include "types.sv"

module nmcu_chiplet 
    import nmcu_pkg::*;
    import nmcu_types::*;
(
    input  logic                    clk,
    input  logic                    rst_n,

    // UCIe Interface - individual signals
    input  logic                    ucie_tx_clk,
    input  logic                    ucie_rx_clk,
    output logic [UCIE_FLIT_WIDTH-1:0] ucie_tx_data,
    output logic                    ucie_tx_valid,
    input  logic                    ucie_tx_ready,
    input  logic [UCIE_FLIT_WIDTH-1:0] ucie_rx_data,
    input  logic                    ucie_rx_valid,
    output logic                    ucie_rx_ready,

    // Memory Interface - individual signals
    output logic                    mem_req_valid,
    input  logic                    mem_req_ready,
    output logic [ADDR_WIDTH-1:0]   mem_req_addr,
    output logic [MEM_DATA_WIDTH-1:0] mem_req_data,
    output logic [2:0]              mem_req_cmd,
    output logic [7:0]              mem_req_burst_len,
    output logic [3:0]              mem_req_id,
    output logic [MEM_DATA_WIDTH/8-1:0] mem_req_be,
    input  logic                    mem_resp_valid,
    output logic                    mem_resp_ready,
    input  logic [MEM_DATA_WIDTH-1:0] mem_resp_data,
    input  logic [3:0]              mem_resp_id,
    input  logic                    mem_resp_error,
    input  logic [1:0]              mem_resp_status,
    input  logic [7:0]              mem_resp_tag,

    // Status and Debug
    output logic [31:0]             status_reg,
    output logic                    ready,
    output logic                    error
);

    // For now, just implement a simple pass-through behavior
    // This allows us to test compilation without complex interface issues
    
    // Status register - simple counter
    logic [31:0] cycle_counter;
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            cycle_counter <= '0;
        end else begin
            cycle_counter <= cycle_counter + 1;
        end
    end
    
    // Simple outputs
    assign status_reg = cycle_counter;
    assign ready = rst_n && (cycle_counter > 32'd100);  // Ready after 100 cycles
    assign error = 1'b0;  // No errors for now
    
    // UCIe outputs - simple loopback for testing
    assign ucie_tx_data = ucie_rx_data;
    assign ucie_tx_valid = ucie_rx_valid;
    assign ucie_rx_ready = ucie_tx_ready;
    
    // Memory interface - simple idle behavior
    assign mem_req_valid = 1'b0;
    assign mem_req_addr = '0;
    assign mem_req_data = '0;
    assign mem_req_cmd = 3'b000;
    assign mem_req_burst_len = '0;
    assign mem_req_id = '0;
    assign mem_req_be = '0;
    assign mem_resp_ready = 1'b1;
    
    // Debug output
    `ifdef SIMULATION
        always @(posedge clk) begin
            if (rst_n && (cycle_counter % 1000 == 0)) begin
                $display("[DUT] Cycle %0d, Ready=%0b", cycle_counter, ready);
            end
        end
        
        initial begin
            $display("[DUT] NMCU Chiplet wrapper initialized");
        end
    `endif

endmodule
